#!/usr/bin/env python3
import asyncio
import httpx

async def test_conda_system():
    async with httpx.AsyncClient(timeout=30.0) as client:
        # Test CodeAct endpoint
        response = await client.post(
            "http://localhost:8101/rpc/tasks",
            json={
                "contextId": "test-conda",
                "taskId": "test-task", 
                "message": {
                    "role": "user",
                    "parts": [{"text": "import numpy as np; print(f'NumPy {np.__version__} working in conda!')"}],
                    "messageId": "test-msg"
                }
            },
            headers={"Accept": "text/event-stream"}
        )
        
        print(f"CodeAct Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Conda environment is working!")
        else:
            print(f"❌ Error: {response.text}")

if __name__ == "__main__":
    asyncio.run(test_conda_system())
