#!/bin/bash

# Setup script for CodeAct virtual environment
# Usage: ./setup_venv.sh [venv_path]

VENV_PATH=${1:-./venvs/codeact}

echo "Creating virtual environment at: $VENV_PATH"

# Create the virtual environment
python -m venv $VENV_PATH

# Activate and install packages
source $VENV_PATH/bin/activate

pip install --upgrade pip
pip install \
    numpy pandas matplotlib seaborn scikit-learn \
    requests beautifulsoup4 jupyter notebook ipython \
    plotly opencv-python tensorflow torch \
    sqlalchemy psycopg2-binary pymongo redis \
    fastapi uvicorn pydantic

echo "Virtual environment created at: $VENV_PATH"
echo "To use this environment, set: CODEACT_VENV_PATH=$VENV_PATH"