#!/bin/bash

# Setup script for CodeAct conda environment
# Usage: ./setup_conda_env.sh [environment_name]

ENV_NAME=${1:-codeact}

echo "Creating conda environment: $ENV_NAME"

# Create the environment
conda create -n $ENV_NAME python=3.11 -y

# Activate and install packages
conda run -n $ENV_NAME pip install \
    numpy pandas matplotlib seaborn scikit-learn \
    requests beautifulsoup4 jupyter notebook ipython \
    plotly opencv-python tensorflow torch \
    sqlalchemy psycopg2-binary pymongo redis \
    fastapi uvicorn pydantic

echo "Conda environment '$ENV_NAME' created successfully!"
echo "To use this environment, set: CODEACT_CONDA_ENV=$ENV_NAME"