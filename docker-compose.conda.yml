services:
  # Manager Agent - Orchestrates other agents
  manager:
    build:
      context: .
      dockerfile: Dockerfile.manager
    container_name: a2a-manager
    ports:
      - "8100:8100"
    environment:
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - MANAGER_AGENT_HOST=0.0.0.0
      - MANAGER_AGENT_PORT=8100
      - MCP_CONFIG_PATH_MANAGER=/app/agents/manager/config/config_mcp.json
      # Agent discovery URLs for Docker network
      - CODEACT_URL=http://codeact:8101
      - RESEARCHER_URL=http://researcher:8102
    volumes:
      - ./agents/manager:/app/agents/manager
      - ./mcp_data:/mcp_data
      - conversation-data:/data/conversations
      - shared-workspace:/workspace:ro  # Read-only access to shared workspace
    depends_on:
      - codeact
    networks:
      - a2a-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8100/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # CodeAct Agent with Conda Environment
  codeact:
    build:
      context: ../agent-codeact
      dockerfile: Dockerfile.conda  # Use the new conda Dockerfile
    container_name: a2a-codeact
    ports:
      - "8101:8101"
    environment:
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - A2A_SERVER_HOST=0.0.0.0
      - A2A_SERVER_PORT=8101
      - A2A_PATH_PREFIX=/rpc
      - A2A_PUBLIC_URL=http://codeact:8101
      # Conda environment configuration
      - CODEACT_CONDA_ENV_PATH=/opt/conda/envs/codeact
      - CODEACT_MCP_FILESYSTEM_PATH=/workspace
      - CODEACT_MCP_TMP_PATH=/tmp
      - CONDA_DEFAULT_ENV=codeact
    volumes:
      - shared-workspace:/workspace  # Full read-write access to workspace
      - conversation-data:/data/conversations
      - conda-cache:/opt/conda/pkgs  # Cache conda packages
    networks:
      - a2a-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8101/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    # Resource limits for code execution safety
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
    # Security constraints
    security_opt:
      - no-new-privileges:true
    tmpfs:
      - /tmp:noexec,nosuid,size=1G

  # Researcher Agent
  researcher:
    build:
      context: .
      dockerfile: Dockerfile.researcher
    container_name: a2a-researcher
    ports:
      - "8102:8102"
    environment:
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - RESEARCHER_AGENT_HOST=0.0.0.0
      - RESEARCHER_AGENT_PORT=8102
    volumes:
      - conversation-data:/data/conversations
      - shared-workspace:/workspace:ro  # Read-only access to shared workspace
    networks:
      - a2a-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8102/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  shared-workspace:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${PWD}/workspace  # Bind to local workspace directory
  conversation-data:
    driver: local
  conda-cache:
    driver: local

networks:
  a2a-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16