import asyncio
import httpx

async def test_endpoint():
    async with httpx.AsyncClient() as client:
        # Test basic endpoint
        response = await client.post(
            "http://localhost:8101/rpc/tasks",
            json={
                "contextId": "test",
                "taskId": "test", 
                "message": {
                    "role": "user",
                    "parts": [{"text": "print('hello from conda')"}],
                    "messageId": "test"
                }
            },
            headers={"Accept": "text/event-stream"},
            timeout=10.0
        )
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ A2A endpoint working!")
        else:
            print(f"❌ Error: {response.text}")

asyncio.run(test_endpoint())
