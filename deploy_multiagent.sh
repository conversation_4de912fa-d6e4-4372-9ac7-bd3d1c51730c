#!/bin/bash
# deploy_multiagent.sh - Production deployment script

set -e

echo "🚀 Deploying Multi-Agent A2A System with Conda Support"

# Create necessary directories
mkdir -p workspace
mkdir -p data/conversations
mkdir -p logs

# Set up environment
if [ ! -f .env ]; then
    echo "⚠️  Creating .env from .env.sample"
    cp .env.sample .env
    echo "Please edit .env with your API keys before continuing"
    exit 1
fi

# Load environment variables
source .env

# Validate required environment variables
if [ -z "$GOOGLE_API_KEY" ]; then
    echo "❌ GOOGLE_API_KEY not set in .env"
    exit 1
fi

echo "🔧 Building Docker images..."

# Build manager agent
echo "Building Manager Agent..."
docker build -t multiagent-manager -f Dockerfile.manager .

# Build codeact agent with conda
echo "Building CodeAct Agent with Conda..."
docker build -t multiagent-codeact -f ../agent-codeact/Dockerfile.conda ../agent-codeact/

# Build researcher agent
echo "Building Researcher Agent..."
docker build -t multiagent-researcher -f Dockerfile.researcher .

echo "🏁 Starting services..."

# Start the multi-agent system
docker compose -f docker-compose.conda.yml up -d

echo "⏳ Waiting for services to be healthy..."

# Wait for health checks
for service in manager codeact researcher; do
    echo "Waiting for $service..."
    timeout=60
    count=0
    while [ $count -lt $timeout ]; do
        if docker compose -f docker-compose.conda.yml ps $service | grep -q "healthy"; then
            echo "✅ $service is healthy"
            break
        fi
        sleep 2
        count=$((count + 2))
    done
    
    if [ $count -ge $timeout ]; then
        echo "❌ $service failed to become healthy"
        docker compose -f docker-compose.conda.yml logs $service
        exit 1
    fi
done

echo "🎉 Multi-Agent A2A System deployed successfully!"

echo "📊 Service Status:"
docker compose -f docker-compose.conda.yml ps

echo "🌐 Service URLs:"
echo "  Manager Agent:  http://localhost:8100"
echo "  CodeAct Agent:  http://localhost:8101"
echo "  Researcher:     http://localhost:8102"

echo "🔍 Agent Discovery URLs:"
echo "  Manager Card:   http://localhost:8100/.well-known/agent.json"
echo "  CodeAct Card:   http://localhost:8101/.well-known/agent.json"
echo "  Researcher Card: http://localhost:8102/.well-known/agent.json"

echo "📁 Workspace: ./workspace (shared across agents)"
echo "💬 Conversations: ./data/conversations"

echo "🚀 To test the system:"
echo "  python enhanced_cli_client.py chat --agent-url http://localhost:8100/rpc/"
echo "  python enhanced_cli_client.py test-conda"
echo "  python enhanced_cli_client.py discover"

echo "📋 To view logs:"
echo "  docker compose -f docker-compose.conda.yml logs -f [service_name]"

echo "🛑 To stop:"
echo "  docker compose -f docker-compose.conda.yml down"

echo "🔧 To rebuild after changes:"
echo "  docker compose -f docker-compose.conda.yml build"
echo "  docker compose -f docker-compose.conda.yml up -d"
