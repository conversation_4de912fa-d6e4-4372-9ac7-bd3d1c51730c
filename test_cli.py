#!/usr/bin/env python3
"""Quick CLI test without external dependencies."""

import asyncio
import httpx
import json
import uuid

async def test_manager():
    """Test manager connection and send simple request."""
    
    # Test health first
    try:
        async with httpx.AsyncClient(timeout=5.0) as client:
            health = await client.get("http://localhost:8100/health")
            print(f"Manager Health: {health.status_code}")
            
            if health.status_code == 200:
                # Send test message
                payload = {
                    "jsonrpc": "2.0",
                    "method": "message/send", 
                    "id": "test1",
                    "params": {
                        "message": {
                            "messageId": str(uuid.uuid4()),
                            "role": "user",
                            "parts": [{"kind": "text", "text": "Hello, write a simple Python function to add two numbers"}],
                            "contextId": "test_context"
                        }
                    }
                }
                
                response = await client.post("http://localhost:8100/rpc", json=payload)
                print(f"Response Status: {response.status_code}")
                
                if response.status_code == 200:
                    result = response.json()
                    print("✅ Manager routing working!")
                    
                    # Show routing info
                    if "result" in result and "history" in result["result"]:
                        for msg in result["result"]["history"]:
                            if msg.get("role") == "agent":
                                for part in msg.get("parts", []):
                                    text = part.get("text", "")[:100]
                                    print(f"Agent: {text}...")
                    
                    # Show final result
                    artifacts = result.get("result", {}).get("artifacts", [])
                    for artifact in artifacts:
                        for part in artifact.get("parts", []):
                            content = part.get("text", "")[:200]
                            print(f"Result: {content}...")
                else:
                    print(f"Error: {response.text}")
                    
    except Exception as e:
        print(f"Connection error: {e}")

if __name__ == "__main__":
    print("🧪 Testing Multi-Agent System...")
    asyncio.run(test_manager())
