import os

import logging
import json
import httpx
import uvicorn
from starlette.responses import JSONResponse

from a2a.server.apps import A2AStarletteApplication
from a2a.server.request_handlers.default_request_handler import <PERSON><PERSON><PERSON><PERSON>equestHandler
from a2a.server.tasks import InMemoryTaskStore
from a2a.server.tasks import InMemoryPushNotifier
from a2a.types import (
    AgentCard,
    AgentSkill,
    AgentCapabilities,
    AgentProvider,
    SecurityScheme,
)

from .agent_executor import SimpleResearchAgentExecutor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# --- Agent Card Definition ---
def load_agent_card(host: str, port: int) -> AgentCard:
    """Loads the agent card from the JSON file and adapts it to a2a.types.AgentCard."""
    try:
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        card_path = os.path.join(
            project_root,
            "well_known", 
            "researcher_agent.json"
        )
        with open(card_path, "r") as f:
            raw_card = json.load(f)

        agent_base_url = f"http://{host}:{port}"

        skills = []
        if "skills" in raw_card and isinstance(raw_card["skills"], list):
            for skill_data in raw_card["skills"]:
                skills.append(
                    AgentSkill(
                        id=skill_data.get("name", "unknown_skill_id"),
                        name=skill_data.get("name", "Unknown Skill"),
                        description=skill_data.get("description", ""),
                        tags=[], 
                    )
                )
        
        capabilities = AgentCapabilities(
            streaming=raw_card.get("streaming", False),
            pushNotifications=raw_card.get("pushNotificationSupport", False),
            stateTransitionHistory=raw_card.get("stateTransitionHistory", True)
        )

        provider = AgentProvider(
            organization=raw_card.get("provider", {}).get("organization", "DefaultOrg"),
            url=raw_card.get("provider", {}).get("url", "http://example.com")
        )

        return AgentCard(
            name=raw_card.get("name", "Researcher Agent"),
            description=raw_card.get("description", "Handles research and information gathering tasks."),
            url=agent_base_url, 
            version=raw_card.get("version", "1.0.0"),
            capabilities=capabilities,
            skills=skills,
            defaultInputModes=["text/plain"], 
            defaultOutputModes=["text/plain", "application/json"],
            provider=provider,
            security=None, 
            securitySchemes=None,
            documentationUrl=raw_card.get("documentationUrl")
        )
    except Exception as e:
        logger.error(f"Error loading Researcher agent card: {e}")
        return AgentCard(
            name="Researcher Agent (Fallback)",
            description="Handles research tasks.",
            url=f"http://{host}:{port}",
            version="0.0.0",
            capabilities=AgentCapabilities(streaming=True, pushNotifications=False, stateTransitionHistory=True),
            skills=[AgentSkill(id="fallback_research_skill", name="Fallback Research Skill", description="Fallback research skill", tags=[])],
            defaultInputModes=["text/plain"],
            defaultOutputModes=["text/plain"],
        )

# --- Environment Variables & Configuration ---
GOOGLE_API_KEY = os.environ.get("GOOGLE_API_KEY")
if not GOOGLE_API_KEY:
    logger.warning("GOOGLE_API_KEY environment variable not set. ResearcherAgent may not function correctly.")

DEFAULT_MCP_CONFIG_PATH = os.path.join(os.path.dirname(__file__), "config", "config_mcp.json")
MCP_CONFIG_PATH = os.environ.get("MCP_CONFIG_PATH_RESEARCHER", DEFAULT_MCP_CONFIG_PATH)

HOST = os.environ.get("RESEARCHER_AGENT_HOST", "0.0.0.0")
PORT = int(os.environ.get("RESEARCHER_AGENT_PORT", "8102"))

# --- Application Setup ---
researcher_agent_card = load_agent_card(HOST, PORT)

researcher_agent_executor = SimpleResearchAgentExecutor(
    api_key=GOOGLE_API_KEY
)

task_store = InMemoryTaskStore()
async_http_client = httpx.AsyncClient()
push_notifier = InMemoryPushNotifier(httpx_client=async_http_client)

request_handler = DefaultRequestHandler(
    agent_executor=researcher_agent_executor,
    task_store=task_store,
    push_notifier=push_notifier
)

application = A2AStarletteApplication(
    agent_card=researcher_agent_card,
    http_handler=request_handler,
)

app = application.build(rpc_url="/rpc")

# Health route AFTER app is created
@app.route("/health", methods=["GET"])
async def health_check(request):
    return JSONResponse({"status": "healthy", "service": "researcher"})

# --- Main Execution ---
if __name__ == "__main__":
    logger.info(f"Starting Researcher Agent server on {HOST}:{PORT}")
    uvicorn.run(app, host=HOST, port=PORT)
