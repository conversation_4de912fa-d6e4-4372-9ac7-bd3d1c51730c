#!/usr/bin/env python3
import asyncio
import json
import httpx

async def test_codeact():
    print("🧪 Testing CodeAct agent...")
    
    # Test health endpoint
    async with httpx.AsyncClient() as client:
        health = await client.get("http://localhost:8101/health")
        print(f"Health check: {health.status_code} - {health.text}")
        
        # Test agent card
        card = await client.get("http://localhost:8101/.well-known/agent.json")
        print(f"Agent card: {card.status_code}")
        if card.status_code == 200:
            data = card.json()
            print(f"Agent: {data['name']} - {data['description']}")
        
        # Test A2A task submission
        task_data = {
            "contextId": "test-123",
            "taskId": "test-456",
            "message": {
                "role": "user",
                "parts": [{"text": "import numpy; print(f'NumPy version: {numpy.__version__}')"}],
                "messageId": "msg-789"
            }
        }
        
        print("\n🔧 Testing conda environment...")
        try:
            response = await client.post(
                "http://localhost:8101/rpc/tasks",
                json=task_data,
                headers={"Accept": "text/event-stream"},
                timeout=30.0
            )
            print(f"Task submission: {response.status_code}")
            if response.status_code != 200:
                text = response.text
                print(f"Error: {text}")
            else:
                print("✅ A2A endpoint responding correctly")
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_codeact())
