# Running Guide - Multi-Agent A2A System

## Architecture Overview

The system consists of three containerized agents:
- **Manager** (port 8100): Central router with MCP tools and agent discovery
- **CodeAct** (port 8101): Coding, file operations, web access
- **Researcher** (port 8102): Research and information gathering

Communication uses JSON-RPC with automatic agent discovery via `.well-known/agent.json` endpoints.

## Quick Start (Docker - Recommended)

```bash
# 1. Setup
git clone <repository>
cd multiagent_A2A
cp .env.sample .env
# Edit .env: add your GOOGLE_API_KEY

# 2. Run
docker compose up --build -d

# 3. Test
curl -X POST http://localhost:8100/rpc \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"message/send","id":"1","params":{"message":{"messageId":"msg1","role":"user","parts":[{"kind":"text","text":"Create a Python function for fibonacci"}],"contextId":"test"}}}'
```

## Development Mode (Local Python)

### Prerequisites
- Python 3.13+ environment
- GOOGLE_API_KEY in environment

### Start Services
```bash
# Set paths
export PYTHONPATH="$PWD:$PWD/a2a-python/src:$PYTHONPATH"

# Start CodeAct (Terminal 1)
cd /path/to/agent-codeact
python codeact_a2a_server.py

# Start Manager (Terminal 2)
cd multiagent_A2A
export MCP_CONFIG_PATH_MANAGER="./agents/manager/config/config_mcp.json"
python -m agents.manager.server

# Start Researcher (Terminal 3)
python -m agents.researcher.server
```

## Testing & Verification

### 1. Health Checks
```bash
curl http://localhost:8100/health  # Manager
curl http://localhost:8101/health  # CodeAct
curl http://localhost:8102/health  # Researcher
```

### 2. Agent Discovery
```bash
# Check agent capabilities
curl http://localhost:8100/.well-known/agent.json
curl http://localhost:8101/.well-known/agent.json
curl http://localhost:8102/.well-known/agent.json
```

### 3. Task Routing Tests
```bash
# Coding task → routes to CodeAct
curl -X POST http://localhost:8100/rpc \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"message/send","id":"code1","params":{"message":{"messageId":"m1","role":"user","parts":[{"kind":"text","text":"Write a function to reverse a string"}],"contextId":"test1"}}}'

# Research task → routes to appropriate agent
curl -X POST http://localhost:8100/rpc \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"message/send","id":"research1","params":{"message":{"messageId":"m2","role":"user","parts":[{"kind":"text","text":"What are the latest AI developments?"}],"contextId":"test2"}}}'
```

## Docker Operations

### Basic Commands
```bash
# Start all services
docker compose up -d

# View logs
docker compose logs -f
docker compose logs -f manager    # Specific service

# Restart service
docker compose restart manager

# Stop all
docker compose down

# Rebuild after changes
docker compose build --no-cache
docker compose up -d
```

### Troubleshooting Docker

**Build failures**:
```bash
docker system prune -f
docker compose down --volumes
docker compose build --no-cache
```

**Network issues**:
```bash
# Check container connectivity
docker compose exec manager curl http://codeact:8101/health
```

**Permission errors**:
```bash
mkdir -p mcp_data/manager_fs
chmod 755 mcp_data/manager_fs
```

## Environment Configuration

### Required Variables (.env)
```bash
GOOGLE_API_KEY=your_gemini_api_key_here
MODEL_NAME=gemini-1.5-pro
MODEL_TEMPERATURE=0.2
```

### Optional Variables
```bash
BRAVE_API_KEY=your_brave_search_key
LOG_LEVEL=INFO
MCP_SERVER_PORT=3000
```

### Development Variables
```bash
export PYTHONPATH="/path/to/multiagent_A2A:/path/to/a2a-python/src:$PYTHONPATH"
export MCP_CONFIG_PATH_MANAGER="./agents/manager/config/config_mcp.json"
```

## Monitoring & Debugging

### Log Locations
```bash
# Docker logs
docker compose logs -f manager

# Development logs (if file logging enabled)
tail -f logs/manager.log
tail -f logs/codeact.log
```

### Common Issues

**Port conflicts**:
```bash
lsof -ti:8100 | xargs kill -9
```

**Agent not responding**:
```bash
# Check if agent is discoverable
curl http://localhost:8101/.well-known/agent.json

# Test direct communication
curl -X POST http://localhost:8101/rpc \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","method":"message/send","id":"direct","params":{"message":{"messageId":"m1","role":"user","parts":[{"kind":"text","text":"Hello"}],"contextId":"test"}}}'
```

**Manager routing issues**:
```bash
# Check agent registration logs
docker compose logs manager | grep "Registered agent"
```

## Performance & Scaling

### Resource Requirements
- **Memory**: 4GB+ available
- **CPU**: 2+ cores recommended
- **Network**: Internet access for model APIs

### Optimization
- Use `--no-cache` builds sparingly (slower)
- Monitor container resource usage: `docker stats`
- Adjust model temperature for faster responses

## CLI Client Usage

```bash
# Interactive client
python cli_client.py

# Direct port access
python cli_client.py 8101  # CodeAct direct
python cli_client.py 8102  # Researcher direct
```

## File System Access

Agents have mapped directories:
- **Manager**: `./mcp_data/manager_fs` → `/mcp_data/manager_fs`
- **CodeAct**: Limited to `/tmp` and `/app` within container

Files placed in `mcp_data/manager_fs` are accessible to the Manager's MCP filesystem tools.

## System Architecture Flow

```
User Request → Manager (port 8100)
    ↓ (analyzes task)
    ├─ CodeAct (port 8101) for coding/technical tasks
    ├─ Researcher (port 8102) for research tasks
    └─ Direct response for simple queries
    ↓ (synthesizes result)
User receives response with artifacts
```

The Manager uses MCP sequential thinking tools for complex routing decisions and maintains conversation context across interactions.