# Dockerfile for Researcher Agent
FROM python:3.13-slim

WORKDIR /app

# System dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl ca-certificates git && \
    rm -rf /var/lib/apt/lists/*

# Copy and install Python dependencies
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Install a2a-sdk package
RUN pip install a2a-sdk>=0.2.3

# Copy agent code
COPY agents/researcher /app/agents/researcher

# Create data directories
RUN mkdir -p /data/conversations

# Set Python path
ENV PYTHONPATH=/app:$PYTHONPATH

EXPOSE 8102

CMD ["python", "-m", "agents.researcher.server"]
