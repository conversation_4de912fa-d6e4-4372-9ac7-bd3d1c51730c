# Dockerfile for Manager Agent
FROM python:3.13-slim

WORKDIR /app

# System dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl ca-certificates git gnupg unzip && \
    rm -rf /var/lib/apt/lists/*

# Install uv for MCP
RUN curl -LsSf https://astral.sh/uv/install.sh | sh
ENV PATH="/root/.local/bin:${PATH}"

# Install Node.js for MCP servers
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - && \
    apt-get install -y nodejs && \
    corepack enable

# Copy and install Python dependencies
COPY requirements.txt ./
RUN pip install --no-cache-dir -r requirements.txt

# Copy agent code
COPY agents/manager /app/agents/manager

# Create data directories
RUN mkdir -p /data/conversations /mcp_data

# Set Python path
ENV PYTHONPATH=/app:$PYTHONPATH

EXPOSE 8100

CMD ["python", "-m", "agents.manager.server"]
