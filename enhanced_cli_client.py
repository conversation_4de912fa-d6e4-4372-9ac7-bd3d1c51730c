#!/usr/bin/env python3
"""
Enhanced CLI Client for Multi-Agent A2A System
Supports conversation history, human-in-the-loop, and agent discovery
"""

import asyncio
import json
import uuid
import sys
from typing import Optional, Dict, Any
from pathlib import Path
import typer
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.live import Live
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.prompt import Prompt, Confirm
from rich.syntax import Syntax
import httpx

app = typer.Typer(help="Multi-Agent A2A System CLI Client")
console = Console()

class ConversationManager:
    """Manages conversation history and state."""
    
    def __init__(self, data_dir: Path = Path("./data/conversations")):
        self.data_dir = data_dir
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.current_conversation: Optional[str] = None
        self.conversation_history: Dict[str, Any] = {}
    
    def start_conversation(self, conversation_id: Optional[str] = None) -> str:
        """Start a new conversation or resume existing."""
        if conversation_id is None:
            conversation_id = str(uuid.uuid4())
        
        self.current_conversation = conversation_id
        
        # Load existing conversation if it exists
        conv_file = self.data_dir / f"{conversation_id}.json"
        if conv_file.exists():
            with open(conv_file, 'r') as f:
                self.conversation_history = json.load(f)
            console.print(f"📂 Resumed conversation: {conversation_id}")
        else:
            self.conversation_history = {
                "id": conversation_id,
                "messages": [],
                "metadata": {
                    "created_at": None,
                    "agents_used": [],
                    "human_interventions": 0
                }
            }
            console.print(f"🆕 Started new conversation: {conversation_id}")
        
        return conversation_id
    
    def save_conversation(self):
        """Save current conversation to disk."""
        if self.current_conversation:
            conv_file = self.data_dir / f"{self.current_conversation}.json"
            with open(conv_file, 'w') as f:
                json.dump(self.conversation_history, f, indent=2)
    
    def add_message(self, role: str, content: str, agent: Optional[str] = None):
        """Add a message to conversation history."""
        message = {
            "role": role,
            "content": content,
            "timestamp": None,
            "agent": agent
        }
        self.conversation_history["messages"].append(message)
        
        if agent and agent not in self.conversation_history["metadata"]["agents_used"]:
            self.conversation_history["metadata"]["agents_used"].append(agent)
        
        if role == "human":
            self.conversation_history["metadata"]["human_interventions"] += 1
    
    def list_conversations(self) -> list:
        """List all saved conversations."""
        conversations = []
        for conv_file in self.data_dir.glob("*.json"):
            try:
                with open(conv_file, 'r') as f:
                    conv_data = json.load(f)
                conversations.append({
                    "id": conv_data["id"],
                    "messages": len(conv_data["messages"]),
                    "agents": conv_data["metadata"]["agents_used"],
                    "interventions": conv_data["metadata"]["human_interventions"]
                })
            except:
                continue
        return conversations

class A2AClient:
    """Client for interacting with A2A agents."""
    
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.session = httpx.AsyncClient(timeout=300.0)
        self.conversation_manager = ConversationManager()
    
    async def discover_agent(self) -> Optional[Dict]:
        """Discover agent capabilities."""
        try:
            response = await self.session.get(f"{self.base_url}/.well-known/agent.json")
            if response.status_code == 200:
                return response.json()
        except:
            pass
        return None
    
    async def send_task(self, message: str, context_id: Optional[str] = None) -> str:
        """Send a task to the agent and handle streaming response."""
        if context_id is None:
            context_id = str(uuid.uuid4())
        
        payload = {
            "jsonrpc": "2.0",
            "method": "message/stream",
            "id": str(uuid.uuid4()),
            "params": {
                "message": {
                    "messageId": str(uuid.uuid4()),
                    "role": "user",
                    "parts": [{"text": message}],
                    "contextId": context_id
                },
                "configuration": {
                    "acceptedOutputModes": ["text/plain"]
                }
            }
        }
        
        headers = {
            "Content-Type": "application/json",
            "Accept": "text/event-stream"
        }
        
        full_response = ""
        
        with console.status("[bold green]Processing...") as status:
            async with self.session.stream(
            "POST",
            f"{self.base_url}/rpc",
                json=payload,
                headers=headers
            ) as response:
                
                if response.status_code != 200:
                    error_text = await response.aread()
                    return f"Error: {response.status_code} - {error_text.decode()}"
                
                # Parse SSE stream
                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        try:
                            event_data = json.loads(line[6:])
                            
                            if event_data.get("type") == "TaskArtifactUpdateEvent":
                                artifact = event_data.get("artifact", {})
                                parts = artifact.get("parts", [])
                                for part in parts:
                                    if "text" in part:
                                        text_chunk = part["text"]
                                        full_response += text_chunk
                                        status.update(f"[bold green]Receiving response... ({len(full_response)} chars)")
                            
                            elif event_data.get("type") == "TaskStatusUpdateEvent":
                                task_status = event_data.get("status", {})
                                state = task_status.get("state")
                                
                                if state == "completed":
                                    break
                                elif state == "requires_human_input":
                                    # Handle human-in-the-loop
                                    human_input = await self.handle_human_intervention(event_data)
                                    if human_input:
                                        # Continue with human input
                                        return await self.send_task(human_input, context_id)
                                
                        except json.JSONDecodeError:
                            continue
        
        return full_response.strip() if full_response.strip() else "Task completed (no output)"
    
    async def handle_human_intervention(self, event_data: Dict) -> Optional[str]:
        """Handle human-in-the-loop interactions."""
        console.print(Panel(
            "🤝 Human intervention required",
            title="Agent Request",
            border_style="yellow"
        ))
        
        status_message = event_data.get("status", {}).get("message")
        if status_message:
            console.print(f"Agent says: {status_message}")
        
        # Prompt for human input
        human_response = Prompt.ask("Your response")
        
        if human_response:
            self.conversation_manager.add_message("human", human_response)
            self.conversation_manager.save_conversation()
            return human_response
        
        return None

@app.command()
def chat(
    agent_url: str = typer.Option("http://localhost:8100", help="Agent URL"),
    conversation_id: Optional[str] = typer.Option(None, help="Resume conversation ID"),
    interactive: bool = typer.Option(True, help="Interactive mode")
):
    """Start an interactive chat session with the multi-agent system."""
    asyncio.run(_chat(agent_url, conversation_id, interactive))

async def _chat(agent_url: str, conversation_id: Optional[str], interactive: bool):
    client = A2AClient(agent_url)
    
    # Discover agent capabilities
    agent_info = await client.discover_agent()
    if agent_info:
        console.print(Panel(
            f"Connected to: {agent_info.get('name', 'Unknown')}\n"
            f"Description: {agent_info.get('description', 'N/A')}\n"
            f"Capabilities: {', '.join([skill['name'] for skill in agent_info.get('skills', [])])}",
            title="🤖 Agent Info",
            border_style="blue"
        ))
    
    # Start or resume conversation
    conv_id = client.conversation_manager.start_conversation(conversation_id)
    
    if interactive:
        console.print("\n💬 Interactive Chat Mode (type 'exit' to quit, 'help' for commands)")
        
        while True:
            try:
                user_input = Prompt.ask("\n[bold blue]You")
                
                if user_input.lower() in ['exit', 'quit']:
                    break
                elif user_input.lower() == 'help':
                    show_help()
                    continue
                elif user_input.lower() == 'save':
                    client.conversation_manager.save_conversation()
                    console.print("💾 Conversation saved")
                    continue
                elif user_input.lower().startswith('workspace'):
                    # Show workspace contents
                    show_workspace()
                    continue
                
                # Add user message to history
                client.conversation_manager.add_message("user", user_input)
                
                # Send to agent
                response = await client.send_task(user_input, conv_id)
                
                # Display response with syntax highlighting for code
                if "```" in response:
                    display_code_response(response)
                else:
                    console.print(Panel(response, title="🤖 Agent Response", border_style="green"))
                
                # Add agent response to history
                client.conversation_manager.add_message("agent", response, agent_info.get('name') if agent_info else None)
                client.conversation_manager.save_conversation()
                
            except KeyboardInterrupt:
                console.print("\n👋 Goodbye!")
                break
            except Exception as e:
                console.print(f"❌ Error: {e}")

def display_code_response(response: str):
    """Display response with code syntax highlighting."""
    parts = response.split("```")
    
    for i, part in enumerate(parts):
        if i % 2 == 0:  # Text part
            if part.strip():
                console.print(part.strip())
        else:  # Code part
            lines = part.strip().split('\n')
            language = lines[0] if lines else "text"
            code = '\n'.join(lines[1:]) if len(lines) > 1 else part
            
            syntax = Syntax(code, language, theme="monokai", line_numbers=True)
            console.print(Panel(syntax, title=f"💻 Code ({language})", border_style="cyan"))

def show_help():
    """Display help information."""
    help_table = Table(title="Available Commands")
    help_table.add_column("Command", style="cyan")
    help_table.add_column("Description", style="white")
    
    help_table.add_row("exit/quit", "Exit the chat session")
    help_table.add_row("save", "Save current conversation")
    help_table.add_row("workspace", "Show workspace contents")
    help_table.add_row("help", "Show this help message")
    
    console.print(help_table)

def show_workspace():
    """Display workspace contents."""
    workspace_path = Path("./workspace")
    if workspace_path.exists():
        files = list(workspace_path.rglob("*"))
        if files:
            workspace_table = Table(title="📁 Workspace Contents")
            workspace_table.add_column("Path", style="cyan")
            workspace_table.add_column("Type", style="white")
            workspace_table.add_column("Size", style="yellow")
            
            for file_path in files[:20]:  # Limit to 20 items
                if file_path.is_file():
                    size = f"{file_path.stat().st_size} bytes"
                    file_type = "File"
                else:
                    size = "-"
                    file_type = "Directory"
                
                workspace_table.add_row(str(file_path.relative_to(workspace_path)), file_type, size)
            
            console.print(workspace_table)
        else:
            console.print("📁 Workspace is empty")
    else:
        console.print("📁 Workspace directory not found")

@app.command()
def conversations():
    """List and manage conversations."""
    conv_manager = ConversationManager()
    conversations = conv_manager.list_conversations()
    
    if conversations:
        conv_table = Table(title="💬 Conversation History")
        conv_table.add_column("ID", style="cyan")
        conv_table.add_column("Messages", style="white")
        conv_table.add_column("Agents Used", style="green")
        conv_table.add_column("Human Interventions", style="yellow")
        
        for conv in conversations:
            conv_table.add_row(
                conv["id"][:8] + "...",
                str(conv["messages"]),
                ", ".join(conv["agents"]),
                str(conv["interventions"])
            )
        
        console.print(conv_table)
    else:
        console.print("📭 No conversations found")

@app.command()
def discover(
    manager_url: str = typer.Option("http://localhost:8100", help="Manager agent URL")
):
    """Discover all available agents in the system."""
    asyncio.run(_discover(manager_url))

async def _discover(manager_url: str):
    """Discover agents and display their capabilities."""
    console.print("🔍 Discovering agents...")
    
    # Known agent URLs
    agent_urls = [
        f"{manager_url}",
        f"{manager_url.replace('8100', '8101')}",  # CodeAct
        f"{manager_url.replace('8100', '8102')}"   # Researcher
    ]
    
    agents_table = Table(title="🤖 Discovered Agents")
    agents_table.add_column("Name", style="cyan")
    agents_table.add_column("Description", style="white")
    agents_table.add_column("Skills", style="green")
    agents_table.add_column("URL", style="yellow")
    
    for url in agent_urls:
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{url}/.well-known/agent.json")
                if response.status_code == 200:
                    agent_info = response.json()
                    skills = ", ".join([skill['name'] for skill in agent_info.get('skills', [])])
                    agents_table.add_row(
                        agent_info.get('name', 'Unknown'),
                        agent_info.get('description', 'N/A')[:50] + "...",
                        skills[:50] + "..." if len(skills) > 50 else skills,
                        url
                    )
        except:
            continue
    
    console.print(agents_table)

@app.command()
def test_conda():
    """Test the conda environment setup."""
    asyncio.run(_test_conda())

async def _test_conda():
    """Test conda environment with a simple task."""
    console.print("🧪 Testing conda environment...")
    
    client = A2AClient("http://localhost:8101")
    
    test_message = """
    Test the conda environment by:
    1. Import numpy, pandas, matplotlib
    2. Create a simple dataset with pandas
    3. Generate a plot and save it to /workspace/test_plot.png
    4. Show the installed package versions
    """
    
    response = await client.send_task(test_message)
    console.print(Panel(response, title="🧪 Conda Test Result", border_style="green"))

if __name__ == "__main__":
    app()
