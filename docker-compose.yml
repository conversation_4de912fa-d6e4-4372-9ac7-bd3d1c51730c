# Docker Compose for Multi-Agent A2A System
version: '3.8'

services:
  # Manager Agent - Orchestrates other agents
  manager:
    build:
      context: .
      dockerfile: Dockerfile.manager
    container_name: a2a-manager
    ports:
      - "8100:8100"
    environment:
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - MANAGER_AGENT_HOST=0.0.0.0
      - MANAGER_AGENT_PORT=8100
      - MCP_CONFIG_PATH_MANAGER=/app/agents/manager/config/config_mcp.json
      # For agent discovery within Docker network
      - CODEACT_URL=http://codeact:8101
      - RESEARCHER_URL=http://researcher:8102
    volumes:
      - ./agents/manager:/app/agents/manager
      - ./mcp_data:/mcp_data
      - conversation-data:/data/conversations
    depends_on:
      - codeact
    networks:
      - a2a-network
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8100/health" ]
      interval: 30s
      timeout: 10s
      retries: 3

  # CodeAct Agent - Code execution with MCP tools and conda environment
  codeact:
    build:
      context: ../agent-codeact
      dockerfile: Dockerfile
    container_name: a2a-codeact
    ports:
      - "8101:8101"
    environment:
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - A2A_SERVER_HOST=0.0.0.0
      - A2A_SERVER_PORT=8101
      - A2A_PATH_PREFIX=/rpc
      - A2A_PUBLIC_URL=http://codeact:8101
      # Conda environment activation
      - CONDA_DEFAULT_ENV=codeact-env
      - PATH=/opt/conda/envs/codeact-env/bin:/opt/conda/bin:/usr/local/bin:/usr/bin:/bin
    volumes:
      - codeact-workspace:/workspace
      - conversation-data:/data/conversations
      # Mount additional directories for file operations
      - codeact-temp:/tmp
    networks:
      - a2a-network
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8101/health" ]
      interval: 30s
      timeout: 10s
      retries: 3

  # Researcher Agent
  researcher:
    build:
      context: .
      dockerfile: Dockerfile.researcher
    container_name: a2a-researcher
    ports:
      - "8102:8102"
    environment:
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - RESEARCHER_AGENT_HOST=0.0.0.0
      - RESEARCHER_AGENT_PORT=8102
    volumes:
      - conversation-data:/data/conversations
    networks:
      - a2a-network
    restart: unless-stopped
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost:8102/health" ]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  codeact-workspace:
    driver: local
  codeact-temp:
    driver: local
  conversation-data:
    driver: local

networks:
  a2a-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
