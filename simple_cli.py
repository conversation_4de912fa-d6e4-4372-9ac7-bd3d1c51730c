#!/usr/bin/env python3
"""Simple CLI for Multi-Agent A2A system (no external deps)."""

import asyncio
import json
import uuid
import sys
try:
    import httpx
except ImportError:
    print("Installing httpx...")
    import subprocess
    subprocess.check_call([sys.executable, "-m", "pip", "install", "httpx"])
    import httpx

async def send_message(text: str, port: int = 8100):
    """Send message to agent."""
    payload = {
        "jsonrpc": "2.0",
        "method": "message/send",
        "id": str(uuid.uuid4()),
        "params": {
            "message": {
                "messageId": str(uuid.uuid4()),
                "role": "user", 
                "parts": [{"kind": "text", "text": text}],
                "contextId": str(uuid.uuid4())
            }
        }
    }
    
    async with httpx.AsyncClient(timeout=60.0) as client:
        response = await client.post(f"http://localhost:{port}/rpc", json=payload)
        return response.json() if response.status_code == 200 else {"error": response.text}

async def main():
    """Simple CLI interface."""
    print("🤖 Multi-Agent A2A System")
    print("Manager(8100) → CodeAct(8101) → Researcher(8102)")
    print("Type 'quit' to exit\n")
    
    while True:
        try:
            user_input = input("You: ")
            if user_input.lower() in ['quit', 'exit']:
                break
            
            print("🔄 Processing...")
            result = await send_message(user_input)
            
            if "error" in result:
                print(f"❌ Error: {result['error']}")
            elif "result" in result:
                # Show routing
                history = result["result"].get("history", [])
                for msg in history:
                    if msg.get("role") == "agent":
                        for part in msg.get("parts", []):
                            text = part.get("text", "")
                            if "delegate" in text.lower():
                                print(f"🔀 {text}")
                
                # Show result
                artifacts = result["result"].get("artifacts", [])
                for artifact in artifacts:
                    for part in artifact.get("parts", []):
                        print(f"🤖 {part.get('text', '')}")
            print()
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(main())
