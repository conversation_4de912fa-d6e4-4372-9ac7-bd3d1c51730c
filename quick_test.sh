#!/bin/bash
# quick_test.sh - Simple test without full deployment

echo "🧪 Quick Testing Without Full Build"

# Test if services are running
echo "Checking running containers..."
docker ps | grep -E "(manager|codeact|researcher)"

# Test manager agent if available
echo "Testing manager agent..."
curl -s http://localhost:8100/health || echo "Manager not running"

# Test codeact agent if available  
echo "Testing codeact agent..."
curl -s http://localhost:8101/health || echo "CodeAct not running"

echo "🔍 Use 'docker ps' to see running containers"
echo "🚀 Run './deploy_multiagent.sh' for full deployment"