#!/bin/bash
# Quick commands for multiagent system

case "$1" in
    clean)
        echo "🧹 Clean restart..."
        docker compose -f docker-compose.conda.yml down
        docker compose -f docker-compose.conda.yml build --no-cache
        docker compose -f docker-compose.conda.yml up -d
        ;;
    quick)
        echo "⚡ Quick restart..."
        docker compose -f docker-compose.conda.yml restart
        ;;
    status)
        docker compose -f docker-compose.conda.yml ps
        curl -s http://localhost:8100/health && echo " ✅ Manager"
        curl -s http://localhost:8101/health && echo " ✅ CodeAct" 
        curl -s http://localhost:8102/health && echo " ✅ Researcher"
        ;;
    test)
        python enhanced_cli_client.py discover
        ;;
    logs)
        docker compose -f docker-compose.conda.yml logs -f ${2:-""}
        ;;
    *)
        echo "Usage: $0 {clean|quick|status|test|logs [service]}"
        ;;
esac
