#!/usr/bin/env python3
"""
Modern CLI client for the Multi-Agent A2A system.
Features: Rich formatting, real-time streaming, conversation history, agent routing display.
"""

import asyncio
import json
import uuid
import sys
from typing import Dict, Any, Optional
from datetime import datetime

try:
    import httpx
    from rich.console import Console
    from rich.panel import Panel
    from rich.text import Text
    from rich.live import Live
    from rich.spinner import Spinner
    from rich.table import Table
    from rich.prompt import Prompt
    from rich.syntax import Syntax
    from rich.markdown import Markdown
    import rich.traceback
    rich.traceback.install()
except ImportError:
    print("Missing dependencies. Install with: pip install httpx rich")
    sys.exit(1)

console = Console()

class AgentClient:
    def __init__(self, base_url: str = "http://localhost:8100"):
        self.base_url = base_url
        self.session_id = str(uuid.uuid4())
        self.conversation_history = []
        self.message_counter = 0
        
    async def send_message(self, text: str) -> Dict[str, Any]:
        """Send message using JSON-RPC protocol."""
        self.message_counter += 1
        
        payload = {
            "jsonrpc": "2.0",
            "method": "message/send",
            "id": f"msg_{self.message_counter}",
            "params": {
                "message": {
                    "messageId": str(uuid.uuid4()),
                    "role": "user",
                    "parts": [{"kind": "text", "text": text}],
                    "contextId": self.session_id
                }
            }
        }
        
        try:
            async with httpx.AsyncClient(timeout=120.0) as client:
                response = await client.post(f"{self.base_url}/rpc", json=payload)
                
                if response.status_code == 200:
                    result = response.json()
                    return result
                else:
                    return {"error": f"HTTP {response.status_code}: {response.text}"}
                    
        except Exception as e:
            return {"error": f"Connection error: {str(e)}"}
    
    def add_to_history(self, role: str, content: str, metadata: Dict = None):
        """Add message to conversation history."""
        self.conversation_history.append({
            "timestamp": datetime.now().strftime("%H:%M:%S"),
            "role": role,
            "content": content,
            "metadata": metadata or {}
        })

class CLIInterface:
    def __init__(self):
        self.client = None
        self.context_mode = "auto"  # auto, manager, codeact, researcher
        
    def display_header(self):
        """Display welcome header."""
        header = Text("🤖 Multi-Agent A2A System", style="bold blue")
        subtitle = Text("Intelligent task routing with Manager → CodeAct → Researcher", style="dim")
        
        console.print(Panel.fit(f"{header}\n{subtitle}", border_style="blue"))
        console.print()
    
    def display_status(self, status: str, style: str = "info"):
        """Display status message."""
        styles = {
            "info": "blue",
            "success": "green", 
            "warning": "yellow",
            "error": "red"
        }
        console.print(f"[{styles.get(style, 'white')}]● {status}[/]")
    
    def display_routing_info(self, result: Dict):
        """Display agent routing information."""
        if "result" in result and "history" in result["result"]:
            history = result["result"]["history"]
            
            # Find delegation messages
            routing_table = Table(title="🔀 Agent Routing", show_header=True)
            routing_table.add_column("Step", style="cyan")
            routing_table.add_column("Agent", style="green")
            routing_table.add_column("Action", style="white")
            
            step = 1
            for msg in history:
                if msg.get("role") == "agent":
                    parts = msg.get("parts", [])
                    for part in parts:
                        text = part.get("text", "")
                        if "Manager is analyzing" in text:
                            routing_table.add_row(str(step), "Manager", "Analyzing request...")
                            step += 1
                        elif "delegate task to" in text:
                            agent_name = text.split("delegate task to ")[-1].split(":")[0]
                            routing_table.add_row(str(step), "Manager", f"→ Delegating to {agent_name}")
                            step += 1
            
            if routing_table.rows:
                console.print(routing_table)
                console.print()
    
    def display_response(self, result: Dict):
        """Display agent response with formatting."""
        if "error" in result:
            console.print(Panel(result["error"], title="❌ Error", border_style="red"))
            return
            
        if "result" not in result:
            console.print(Panel("No result received", title="⚠️ Warning", border_style="yellow"))
            return
            
        # Display routing info
        self.display_routing_info(result)
        
        # Display artifacts
        artifacts = result["result"].get("artifacts", [])
        for artifact in artifacts:
            parts = artifact.get("parts", [])
            for part in parts:
                content = part.get("text", "")
                
                # Try to detect code and format it
                if self.is_code_content(content):
                    self.display_code(content)
                else:
                    # Regular response
                    panel_title = f"🤖 {artifact.get('name', 'Agent Response')}"
                    console.print(Panel(content, title=panel_title, border_style="green"))
        
        # Display conversation context
        context_id = result["result"].get("contextId")
        if context_id:
            console.print(f"[dim]Context: {context_id}[/]")
    
    def is_code_content(self, content: str) -> bool:
        """Detect if content contains code."""
        code_indicators = ["def ", "function ", "class ", "import ", "from ", "```", "def(", "=>"]
        return any(indicator in content for indicator in code_indicators)
    
    def display_code(self, content: str):
        """Display code with syntax highlighting."""
        # Extract language and code from markdown code blocks
        lines = content.split('\n')
        in_code_block = False
        language = "python"  # default
        code_lines = []
        
        for line in lines:
            if line.startswith('```'):
                if not in_code_block:
                    in_code_block = True
                    if len(line) > 3:
                        language = line[3:].strip()
                else:
                    in_code_block = False
            elif in_code_block:
                code_lines.append(line)
        
        if code_lines:
            code = '\n'.join(code_lines)
            syntax = Syntax(code, language, theme="monokai", line_numbers=True)
            console.print(Panel(syntax, title="📝 Code", border_style="cyan"))
        else:
            # No code blocks found, display as markdown
            console.print(Panel(Markdown(content), title="📝 Response", border_style="green"))
    
    def display_help(self):
        """Display help information."""
        help_table = Table(title="🆘 Available Commands")
        help_table.add_column("Command", style="cyan")
        help_table.add_column("Description", style="white")
        
        help_table.add_row("/help", "Show this help message")
        help_table.add_row("/history", "Show conversation history")
        help_table.add_row("/clear", "Clear conversation history")
        help_table.add_row("/status", "Check agent connectivity")
        help_table.add_row("/switch <port>", "Switch to different agent")
        help_table.add_row("/quit", "Exit the client")
        help_table.add_row("", "")
        help_table.add_row("Example tasks:", "")
        help_table.add_row("", "• Write a Python function for...")
        help_table.add_row("", "• Research the latest developments in...")
        help_table.add_row("", "• Create a web scraper for...")
        
        console.print(help_table)
    
    def display_history(self):
        """Display conversation history."""
        if not self.client.conversation_history:
            console.print("[dim]No conversation history yet.[/]")
            return
            
        history_table = Table(title="📚 Conversation History", show_header=True)
        history_table.add_column("Time", style="dim")
        history_table.add_column("Role", style="cyan") 
        history_table.add_column("Message", style="white")
        
        for entry in self.client.conversation_history[-10:]:  # Last 10 messages
            content = entry["content"][:100] + "..." if len(entry["content"]) > 100 else entry["content"]
            history_table.add_row(
                entry["timestamp"],
                entry["role"].title(),
                content
            )
        
        console.print(history_table)
    
    async def check_status(self):
        """Check agent connectivity."""
        ports = [8100, 8101, 8102]
        agent_names = ["Manager", "CodeAct", "Researcher"]
        
        status_table = Table(title="🔍 Agent Status", show_header=True)
        status_table.add_column("Agent", style="cyan")
        status_table.add_column("Port", style="white")
        status_table.add_column("Status", style="green")
        
        async with httpx.AsyncClient(timeout=5.0) as client:
            for port, name in zip(ports, agent_names):
                try:
                    response = await client.get(f"http://localhost:{port}/health")
                    status = "🟢 Online" if response.status_code == 200 else "🔴 Error"
                except:
                    status = "🔴 Offline"
                
                status_table.add_row(name, str(port), status)
        
        console.print(status_table)
    
    async def run(self):
        """Main CLI loop."""
        self.display_header()
        
        # Get connection details with validation
        while True:
            port_input = Prompt.ask("Connect to agent port", default="8100")
            try:
                port = int(port_input)
                if port < 1 or port > 65535:
                    console.print("[red]Port must be between 1-65535[/]")
                    continue
                break
            except ValueError:
                console.print(f"[red]'{port_input}' is not a valid port number[/]")
                continue
        
        base_url = f"http://localhost:{port}"
        
        self.client = AgentClient(base_url)
        
        # Test connection
        with console.status("[bold blue]Testing connection..."):
            try:
                async with httpx.AsyncClient(timeout=5.0) as test_client:
                    response = await test_client.get(f"{base_url}/health")
                    if response.status_code == 200:
                        self.display_status(f"✅ Connected to agent on port {port}", "success")
                    else:
                        self.display_status(f"⚠️ Agent responded with status {response.status_code}", "warning")
            except Exception as e:
                self.display_status(f"❌ Connection failed: {e}", "error")
                return
        
        console.print("\n[dim]Type your message or /help for commands[/]")
        console.print("[dim]Examples: 'Write a Python function' or 'Research quantum computing'[/]\n")
        
        while True:
            try:
                user_input = Prompt.ask("[bold blue]You")
                
                if not user_input.strip():
                    continue
                
                # Handle commands
                if user_input.startswith('/'):
                    command = user_input[1:].lower()
                    
                    if command == 'help':
                        self.display_help()
                    elif command == 'history':
                        self.display_history()
                    elif command == 'clear':
                        self.client.conversation_history.clear()
                        self.display_status("Conversation history cleared", "success")
                    elif command == 'status':
                        await self.check_status()
                    elif command.startswith('switch '):
                        new_port = command.split(' ')[1]
                        self.client = AgentClient(f"http://localhost:{new_port}")
                        self.display_status(f"Switched to port {new_port}", "success")
                    elif command in ['quit', 'exit']:
                        console.print("\n[bold blue]Thanks for using Multi-Agent A2A! 👋[/]")
                        break
                    else:
                        console.print(f"[red]Unknown command: {command}[/]")
                    continue
                
                # Add user message to history
                self.client.add_to_history("user", user_input)
                
                # Send message with loading indicator
                with Live(Spinner("dots", text="🤖 Agent processing..."), console=console):
                    result = await self.client.send_message(user_input)
                
                # Display response
                self.display_response(result)
                
                # Add agent response to history
                if "result" in result and "artifacts" in result["result"]:
                    for artifact in result["result"]["artifacts"]:
                        for part in artifact.get("parts", []):
                            if part.get("text"):
                                self.client.add_to_history("agent", part["text"])
                
                console.print()
                
            except KeyboardInterrupt:
                console.print("\n[bold blue]Goodbye! 👋[/]")
                break
            except Exception as e:
                console.print(f"[red]Error: {e}[/]")

async def main():
    """Entry point."""
    interface = CLIInterface()
    await interface.run()

if __name__ == "__main__":
    asyncio.run(main())
